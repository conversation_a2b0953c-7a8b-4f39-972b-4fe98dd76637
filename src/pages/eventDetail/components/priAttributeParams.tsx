import React, { useEffect, useState } from 'react';
import { Table, Input, Button, Popconfirm, Form, Select, Icon, message } from 'antd';
import styles from './index.less';

const { Option } = Select;

const EditableTable = Form.create()(
  ({ form, dataParams, onParamsValueChange, pageStatus, priAttributeParamVO }) => {
    const [data, setData] = useState(dataParams);
    const handleCellChange = (key, dataIndex, value) => {
      const newData = [...data];
      const target = newData.find(item => item.key === key);

      if (target) {
        target[dataIndex] = value;
        setData(newData);
        onParamsValueChange(newData);
      }
    };
    // 字符串/数值/枚举/日期（字符串类型枚举待确认）
    const fieldTypeOrgEnum = [
      { key: 1, value: '字符串' },
      { key: 2, value: '数值' },
      { key: 3, value: '枚举类型' },
    ];
    const columns = [
      {
        title: '属性中文名',
        dataIndex: 'fieldName',
        align: 'center',
        className: styles.tableColumn,
        render: (text, record) => (
          <>
            {pageStatus === 'detail' ? (
              <EditableCellText
                value={text}
                onChange={value => handleCellChange(record.key, 'fieldName', value)}
                form={form}
                inputName={`${record.key}_fieldName`}
              />
            ) : (
              <EditableCellInput
                value={text}
                onChange={value => handleCellChange(record.key, 'fieldName', value)}
                form={form}
                inputName={`${record.key}_fieldName`}
                disabled={['review', 'detail'].includes(pageStatus)}
              />
            )}
          </>
        ),
      },
      {
        title: '属性名',
        dataIndex: 'customizeKey',
        align: 'center',
        className: styles.tableColumn,
        render: (text, record) => (
          <>
            {pageStatus === 'detail' ? (
              <EditableCellText
                value={text.length ? text : ''}
                onChange={value => handleCellChange(record.key, 'customizeKey', value)}
                form={form}
                inputName={`${record.key}_customizeKey`}
              />
            ) : (
              <EditableCellInput
                value={text}
                onChange={value => handleCellChange(record.key, 'customizeKey', value)}
                form={form}
                inputName={`${record.key}_customizeKey`}
                disabled={
                  ['edit', 'review', 'detail'].includes(pageStatus) &&
                  record.key < priAttributeParamVO?.length
                }
                customRules={[
                  {
                    pattern: /^[^0-9]/,
                    message: '不支持以数字开头',
                  },
                  {
                    pattern: /^[a-zA-Z0-9_]*$/,
                    message: '属性名仅支持字母、数字、下划线',
                  },
                  {
                    max: 30,
                    message: '最长支持30字符',
                  },
                ]}
              />
            )}
          </>
        ),
      },
      {
        title: '属性类型',
        dataIndex: 'fieldTypeOrg',
        align: 'center',
        width: 200,
        className: styles.tableColumn,

        render: (text, record) => (
          <>
            {pageStatus === 'detail' ? (
              <EditableCellText
                value={fieldTypeOrgEnum.find(item => item.key === text)?.value}
                // onChange={value => handleCellChange(record.key, 'fieldTypeOrg', value)}
                form={form}
                inputName={`${record.key}_fieldTypeOrg`}
              />
            ) : (
              <EditableCellSelect
                value={text}
                onChange={value => handleCellChange(record.key, 'fieldTypeOrg', value)}
                form={form}
                inputName={`${record.key}_fieldTypeOrg`}
                EnumType={fieldTypeOrgEnum}
                handleCellChange={handleCellChange}
                recodeKey={record.key}
                disabled={
                  ['edit', 'review', 'detail'].includes(pageStatus) &&
                  record.key < priAttributeParamVO?.length
                }
              />
            )}
          </>
        ),
      },
      {
        title: '值',
        dataIndex: 'remarks',
        align: 'center',
        className: styles.tableColumn,
        render: (text, record) => {
          let required = true;
          if ([1, 2].includes(record?.fieldTypeOrg)) {
            required = false;
          }
          return (
            <>
              {pageStatus === 'detail' ? (
                <EditableCellText
                  value={text}
                  onChange={value => handleCellChange(record.key, 'remarks', value)}
                  form={form}
                  inputName={`${record.key}_remarks`}
                />
              ) : (
                <EditableCellInput
                  value={text}
                  onChange={value => handleCellChange(record.key, 'remarks', value)}
                  form={form}
                  inputName={`${record.key}_remarks`}
                  disabled={['review', 'detail'].includes(pageStatus)}
                  requiredType={required}
                />
              )}
            </>
          );
        },
      },
      {
        title: '示例',
        dataIndex: 'example',
        align: 'center',
        className: styles.tableColumn,

        render: (text, record) => {
          const exampleEnmu = [
            { key: 1, value: '非必填，如：筛选项中的订单ID' },
            { key: 2, value: '非必填，如：筛选项中的订单金额' },
            { key: 3, value: '必填，字符串，如：man:男,women:女' },
          ];
          const exampleValue = exampleEnmu.find(item => item.key === record?.fieldTypeOrg)?.value;
          const exampleValueShow = exampleValue?.length ? exampleValue : '';
          return (
            <EditableCellText
              value={exampleValueShow}
              onChange={value => handleCellChange(record.key, 'example', value)}
              form={form}
              inputName={`${record.key}_example`}
            />
          );
        },
      },
      {
        title: '操作',
        dataIndex: 'action',
        align: 'center',
        className: styles.tableColumn,

        render: (_, record) => (
          <Popconfirm
            title="确定要删除吗？"
            onConfirm={() => handleDelete(record.key)}
            disabled={
              ['edit', 'review', 'detail'].includes(pageStatus) &&
              record.key < priAttributeParamVO?.length
            }
          >
            <Button
              type="link"
              disabled={
                ['edit', 'review', 'detail'].includes(pageStatus) &&
                record.key < priAttributeParamVO?.length
              }
            >
              删除
            </Button>
          </Popconfirm>
        ),
      },
    ];

    const handleDelete = key => {
      const newData = data.filter(item => item.key !== key);
      setData(newData);
      onParamsValueChange(newData);
    };

    const handleAddRow = () => {
      const newRowKey = (data.length + 1).toString();
      if (data.length < 20) {
        form.validateFields((err, values) => {
          if (!err) {
            const newRow = {
              key: newRowKey,
              fieldName: '',
              fieldTypeOrg: '',
              remarks: '',
              example: '',
              customizeKey: '',
            };
            setData([...data, newRow]);
            onParamsValueChange([...data, newRow]);
          }
        });
      } else {
        message.error('最多允许添加20条自定义属性');
      }
    };
    // dataParams更新不及时
    useEffect(() => {
      setData(dataParams);
    }, [dataParams]);

    return (
      <div>
        <Table bordered dataSource={data} columns={columns} pagination={false} />
        {pageStatus !== 'review' && pageStatus !== 'detail' && (
          <div className={styles.buttonDashed} onClick={handleAddRow} style={{ cursor: 'pointer' }}>
            <Icon type="plus" />
            添加自定义属性
          </div>
        )}
      </div>
    );
  },
);

const EditableCellText = ({ value, onChange, form, inputName }) => {
  useEffect(() => {
    if (onChange) {
      onChange(value);
    }
  }, [value]);
  return (
    <Form.Item style={{ marginBottom: 0 }}>
      {form.getFieldDecorator(inputName, {
        initialValue: value,
      })(<div>{value?.length ? value : '--'}</div>)}
    </Form.Item>
  );
};

const EditableCellInput = ({
  value,
  onChange,
  form,
  inputName,
  disabled,
  requiredType = true,
  customRules = [], // 直接接收 Ant Design Form 原生支持的校验规则数组
}) => {
  const handleInputChange = e => {
    const newValue = e.target.value;
    onChange(newValue);
  };

  // 构建校验规则数组
  const buildRules = () => {
    const rules = [];
    // 基础必填校验
    if (requiredType) {
      rules.push({ required: true, message: '请输入' });
    }
    // 添加自定义校验规则 - 直接使用外部传入的规则
    if (customRules && customRules.length > 0) {
      rules.push(...customRules);
    }
    return rules;
  };

  return (
    <Form.Item style={{ marginBottom: 0 }}>
      {form.getFieldDecorator(inputName, {
        initialValue: value,
        rules: buildRules(),
        validateFirst: true,
      })(<Input onChange={handleInputChange} disabled={disabled} />)}
    </Form.Item>
  );
};
const EditableCellSelect = ({ value, onChange, form, inputName, EnumType, disabled }) => {
  const valueInit = EnumType.find(item => item.key === value);
  const handleSelectChange = value => {
    onChange(EnumType.find(item => item.key === value)?.key);
  };
  return (
    <Form.Item style={{ marginBottom: 0 }}>
      {form.getFieldDecorator(inputName, {
        initialValue: valueInit?.key,
        rules: [{ required: true, message: '请输入' }],
      })(
        <Select onChange={handleSelectChange} style={{ minWidth: 150 }} disabled={disabled}>
          {EnumType.map(item => (
            <Option value={item.key} key={item.key}>
              {item.value}
            </Option>
          ))}
        </Select>,
      )}
    </Form.Item>
  );
};

export default EditableTable;
