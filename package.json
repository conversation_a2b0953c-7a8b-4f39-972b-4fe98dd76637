{"name": "invest", "version": "1.0.1", "private": true, "description": "An out-of-box UI solution for enterprise applications", "scripts": {"analyze": "cross-env ANALYZE=1 umi build", "build": "umi build", "build:daily": "cross-env deployEnv=daily npm run build cdn withVersion", "build:prepare": "cross-env deployEnv=prepare npm run build cdn publicCdn withVersion", "build:publish": "cross-env deployEnv=publish npm run build cdn publicCdn withVersion", "deploy:daily": "npm run build:daily && uploadToCDN --output=dist --tenantId=1 --destPath=admin --env=test cdn withVersion", "deploy:prepare": "npm run build:prepare && uploadToCDN --output=dist --tenantId=1 --destPath=admin --env=public cdn withVersion", "deploy:publish": "npm run build:publish && uploadToCDN --output=dist --tenantId=1 --destPath=admin --env=public cdn withVersion", "fetch:blocks": "fetch-blocks", "format-imports": "import-sort --write '**/*.{js,jsx,ts,tsx}'", "gh-pages": "cp CNAME ./dist/ && gh-pages -d dist", "lint": "npm run lint:js && npm run lint:style && npm run lint:prettier", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src && npm run lint:style", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "check-prettier lint", "lint:style": "stylelint --fix \"src/**/*.less\" --syntax less", "prettier": "prettier -c --write **/*", "start": "cross-env deployEnv=dev umi dev", "start:no-mock": "cross-env MOCK=none npm start", "test": "umi test", "test:all": "node ./tests/run-tests.js", "test:component": "umi test ./src/components"}, "husky": {"hooks": {}}, "lint-staged": {"**/*.less": "stylelint --syntax less", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write", "git add"], "**/*.{js,jsx}": "npm run lint-staged:js", "**/*.{js,ts,tsx}": "npm run lint-staged:js"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/icons": "^5.2.6", "@ant-design/pro-layout": "4.9.1", "@antv/data-set": "0.10.2", "@antv/g2": "^5.1.3", "@antv/g2plot": "^2.4.31", "@antv/g6": "3.2.4", "@blmcp/ui": "^1.0.19-react16.2", "@leopard/umi-plugin-sso-auth": "0.0.24", "ali-mns": "2.6.8", "amap-js": "1.2.1", "ant-design-pro": "2.3.2", "antd": "3.26.0", "axios": "0.19.0", "benz-amr-recorder": "1.1.2", "bizcharts": "^3.5.3-beta.0", "bizcharts-plugin-slider": "^2.1.1-beta.1", "blueimp-md5": "2.12.0", "braft-editor": "2.3.8", "chalk": "3.0.0", "classnames": "2.2.6", "copy-to-clipboard": "3.2.0", "dayjs": "^1.11.10", "dingtalk-jsapi": "2.8.33", "dva": "2.4.1", "echarts": "4.5.0", "echarts-for-react": "2.0.15-beta.0", "gaea-editor": "2.3.26", "gulp": "4.0.2", "gulp-ssh": "0.7.0", "gulp-tar": "3.1.0", "highlight.js": "9.16.2", "html2canvas": "1.0.0-rc.5", "immutable": "4.0.0-rc.12", "js-cookie": "2.2.1", "js-model": "1.6.2", "lodash": "4.17.15", "lodash-decorators": "6.0.1", "markdown-it": "10.0.0", "marked": "0.7.0", "memoize-one": "5.1.1", "moment": "2.24.0", "omit.js": "1.0.2", "path-to-regexp": "6.1.0", "prop-types": "15.7.2", "qs": "6.9.1", "query-string": "^6.9.0", "react": "17.0.2", "react-amap": "1.2.8", "react-color": "2.17.3", "react-container-query": "0.11.0", "react-contenteditable": "3.3.2", "react-copy-to-clipboard": "5.0.2", "react-document-title": "2.0.3", "react-dom": "17.0.2", "react-json-view": "1.19.1", "react-lazyload": "2.6.5", "react-markdown-editor-lite": "0.5.0", "react-media": "1.10.0", "react-media-hook2": "1.1.2", "react-tiny-virtual-list": "^2.2.0", "redux": "4.0.4", "spark-md5": "3.0.0", "then-sleep": "1.0.1", "umi": "2.12.3", "umi-plugin-antd-theme": "1.0.15", "umi-plugin-pro-block": "1.3.6", "umi-plugin-react": "1.14.6", "umi-request": "1.2.11", "underscore": "1.9.1", "virtualizedtableforantd": "0.6.2", "wangeditor": "3.1.1", "watermark-dom": "2.2.1", "xlsx": "0.15.3", "xlsx-oc": "1.0.2"}, "devDependencies": {"@ant-design/colors": "3.2.2", "@ant-design/pro-cli": "1.0.15", "@types/classnames": "2.2.9", "@types/history": "4.7.3", "@types/jest": "24.0.23", "@types/lodash": "4.14.149", "@types/qs": "6.9.0", "@types/react": "16.9.13", "@types/react-document-title": "2.0.3", "@types/react-dom": "16.9.4", "@umijs/fabric": "1.2.1", "bluebird": "^3.7.2", "chalk": "3.0.0", "check-prettier": "1.0.3", "cross-env": "^6.0.3", "cross-port-killer": "1.2.1", "enzyme": "3.10.0", "eslint": "6.7.2", "fetch-blocks": "1.0.4", "form-data": "^4.0.1", "fs-extra": "^8.1.0", "gh-pages": "2.1.1", "husky": "3.1.0", "import-sort-cli": "6.0.0", "import-sort-parser-babylon": "6.0.0", "import-sort-parser-typescript": "6.0.0", "import-sort-style-module": "6.0.0", "jsdom-global": "3.0.2", "less-loader": "5.0.0", "lint-staged": "9.5.0", "mockjs": "1.1.0", "node-fetch": "2.6.0", "prettier": "1.19.1", "pro-download": "1.0.1", "request": "^2.88.0", "slash2": "2.0.0", "stylelint": "12.0.0", "tar": "^5.0.5", "umi-plugin-ga": "1.1.5", "umi-plugin-pro": "1.0.2", "umi-types": "0.5.7", "webpack-theme-color-replacer": "1.3.2", "yargs": "^16.0.3"}, "engines": {"node": ">=10.0.0"}, "checkFiles": ["src/**/*.js*", "src/**/*.ts*", "src/**/*.less", "config/**/*.js*", "scripts/**/*.js"]}